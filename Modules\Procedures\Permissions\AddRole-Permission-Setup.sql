-- =====================================================
-- AddRole Form Permission Setup Script
-- =====================================================
-- This script adds the AddRole form to the permission system
-- since it's a ChildForm and not automatically discovered
-- by the FormDiscoveryService (which only scans MainForms)
-- =====================================================

-- [AddRoleFormPermissions] --
-- Add AddRole form permissions for all existing roles
-- Default to no permissions (false) for security
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 
    r.role_id, 
    'AddRole',
    false,  -- read_permission: default to false
    false,  -- new_permission: default to false  
    false,  -- edit_permission: default to false
    false,  -- delete_permission: default to false
    false   -- print_permission: default to false
FROM roles r
WHERE NOT EXISTS (
    SELECT 1 FROM role_permissions rp
    WHERE rp.role_id = r.role_id AND rp.form_name = 'AddRole'
);
-- [End] --

-- [GrantAdministratorAddRolePermissions] --
-- Grant Administrator role full permissions for AddRole form
-- Administrators should be able to manage roles
UPDATE role_permissions 
SET 
    read_permission = true,
    new_permission = true,
    edit_permission = true,
    delete_permission = true,
    print_permission = true
WHERE form_name = 'AddRole' 
AND role_id = (SELECT role_id FROM roles WHERE role_name = 'Administrator');
-- [End] --

-- [GrantManagerAddRolePermissions] --
-- Grant Manager role limited permissions for AddRole form
-- Managers can create and edit roles but not delete them
UPDATE role_permissions 
SET 
    read_permission = true,
    new_permission = true,
    edit_permission = true,
    delete_permission = false,  -- Managers cannot delete roles
    print_permission = true
WHERE form_name = 'AddRole' 
AND role_id = (SELECT role_id FROM roles WHERE role_name = 'Manager');
-- [End] --

-- [VerifyAddRolePermissions] --
-- Verify AddRole permissions were created correctly
SELECT 
    r.role_name,
    rp.form_name,
    rp.read_permission,
    rp.new_permission,
    rp.edit_permission,
    rp.delete_permission,
    rp.print_permission
FROM role_permissions rp
JOIN roles r ON rp.role_id = r.role_id
WHERE rp.form_name = 'AddRole'
ORDER BY r.role_name;
-- [End] --

-- =====================================================
-- NOTES:
-- 1. This script should be run after the main RBAC setup
-- 2. AddRole form will now respect permission system
-- 3. Only users with appropriate permissions can use AddRole
-- 4. Administrator and Manager roles get appropriate access
-- 5. User and ReadOnly roles get no access by default
-- =====================================================
